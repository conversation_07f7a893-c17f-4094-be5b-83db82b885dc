### 功能列表



#### 平台管理端功能
 

| 模块 <img width=80/> | 功能    |
|--------------------|-----------------------------------------------------------------| 
| 首页                 | 平台基础信息统计、待办事项                                                   |
| 会员                 | 会员列表、评价列表、积分历史、会员资金、会员充值                                        |
| 订单                 | 商品订单、虚拟订单、订单售后、订单投诉、售后原因维护、收款流水、退款流水                            |
| 商品                 | 商品列表、商品审核、商品分类、商品品牌、商品规格、商品计量单位                                 |
| 促销                 | 优惠券、券活动（每日&每月&每周&邀新 赠券）、秒杀活动、砍价活动、拼团活动、积分商品                     |
| 店铺                 | 店铺管理、店铺审核、店铺结算、店铺对账                                             |
| 运营                 | 楼层装修、分销商管理、文章管理、意见反馈、站内信、短信、搜索热词管理                              |
| 统计                 | 会员统计、订单统计、流量统计、商品销量统计                                           |
| 设置                 | 菜单管理、角色管理、部门管理、管理员管理、系统设置、行政地区管理、OSS管理、联合登陆、支付、物流公司、敏感词、验证码资源 |



#### 卖家功能


| 模块 <img width=80/> | 功能                            |
|----|-------------------------------| 
| 首页 | 店铺基础信息统计、待办事项、店铺公告            |
| 商品 | 商品发布、商品列表、商品模板、店铺分类           |
| 订单 | 商品订单、虚拟订单、订单评价、订单投诉、退款申请、退货申请 |
| 财务 | 店铺对账、店铺结算、发票管理                |
| 促销 | 优惠券、满额优惠、秒杀、拼团 、分销商品、分校订单     |
| 统计 |单统计、流量统计、商品销量统计                                         |

| 设置 | 配送公司、物流模板、店铺设置、店铺自提设置、PC装修、移动端装修、店员管理、部门管理、角色管理 |
| 消息 | 站内信 |


### 商城前端功能展示

#### 商城移动端


### 技术选型

#### 架构图


##### 后台技术选型

| 说明           | 框架            | 说明           |               |
| -------------- | --------------- | -------------- | ------------- |
| 基础框架       | Spring Boot     | MVC框架        | Spring MVC    |
| 持久框架       | Mybatis-Plus    | 程序构建       | Maven         |
| 关系型数据库   | MySQL           | 消息中间件AMQP | RocketMQ      |
| 缓存           | Redis +MongoDB  | 搜索引擎       | Elasticsearch |
| 安全框架       | Spring Security | 数据库连接池   | Druid         |
| 数据库分库分表 | sharding        | 定时任务       | xxl-job       |
| 负载均衡       | Nginx           | 静态资源       | 阿里云OSS     |
| 短信           | 阿里云短信      | 认证           | JWT           |
| 日志处理       | Log4j           | 接口规范       | RESTful       |

##### 前端-运营后台、店铺后台

| 说明       | 框架       | 说明       | 框架    |
| ---------- | ---------- | ---------- | ------- |
| 构建工具   | webpack    | JS版本     | ES6     |
| 基础JS框架 | Vue.js     | 视频播放器 | Dplayer |
| 路由管理   | Vue Router | 状态管理   | Vuex    |
| 基础UI库   | iView      | UI界面基于 | iView   |
| 网络请求   | axios      |            |         |

##### 前端-移动端

| 说明      | 架构    | 说明     | 架构    |
| --------- | ------- | -------- | ------- |
| 基础UI库  | uViewui | 基础框架 | uni-app |
| CSS预处理 | scss    | 地图引擎 | amap    |

### 版本升级

```
系统后续会提供多场景解决方案。
更多架构：微服务、Saas、中台等，都会支持。 支持差价升级商业授权
```