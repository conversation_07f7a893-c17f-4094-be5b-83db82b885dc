package cn.lili.controller.common;

import cn.lili.cache.limit.annotation.LimitPoint;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.OcrResponseUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.ocr.entity.dto.BusinessLicenseInfo;
import cn.lili.modules.ocr.entity.dto.IdCardInfo;
import cn.lili.modules.ocr.service.OcrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * OCR控制器
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Slf4j
@RestController
@Api(tags = "OCR身份证和营业执照识别接口")
@RequestMapping("/common/common/ocr")
public class OcrController {

    @Autowired
    private OcrService ocrService;

    @ApiOperation(value = "身份证正面识别", notes = "上传身份证正面图片，识别并返回对应信息")
    @PostMapping(value = "/idcard/front")
    @LimitPoint(name = "ocr_idcard_front", key = "ocr_idcard_front")
    public Object idcardFront(@ApiParam(value = "身份证正面图片文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            logUserAction("身份证正面识别");

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            IdCardInfo idCardInfo = ocrService.recognizeIdCardFront(file);

            if (idCardInfo == null || !idCardInfo.isValidFront()) {
                return OcrResponseUtil.buildFailureResponse("身份证正面识别失败: 图片不清晰或非身份证图片");
            }

            return OcrResponseUtil.buildIdCardFrontSuccess(idCardInfo);

        } catch (ServiceException e) {
            log.error("身份证正面识别业务异常: {}", e.getMessage(), e);
            return OcrResponseUtil.buildFailureResponse("身份证正面识别失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("身份证正面识别系统异常", e);
            return OcrResponseUtil.buildFailureResponse("身份证正面识别失败: 系统内部错误");
        }
    }

    @ApiOperation(value = "身份证背面识别", notes = "上传身份证背面图片，识别并返回对应信息")
    @PostMapping(value = "/idcard/back")
    @LimitPoint(name = "ocr_idcard_back", key = "ocr_idcard_back")
    public Object idcardBack(@ApiParam(value = "身份证背面图片文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            logUserAction("身份证背面识别");

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            IdCardInfo idCardInfo = ocrService.recognizeIdCardBack(file);

            if (idCardInfo == null || !idCardInfo.isValidBack()) {
                return OcrResponseUtil.buildFailureResponse("身份证背面识别失败: 图片不清晰或非身份证图片");
            }

            return OcrResponseUtil.buildIdCardBackSuccess(idCardInfo);

        } catch (ServiceException e) {
            log.error("身份证背面识别业务异常: {}", e.getMessage(), e);
            return OcrResponseUtil.buildFailureResponse("身份证背面识别失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("身份证背面识别系统异常", e);
            return OcrResponseUtil.buildFailureResponse("身份证背面识别失败: 系统内部错误");
        }
    }

    @ApiOperation(value = "身份证识别（单面）", notes = "上传身份证正面或反面图片，接口会尝试识别并返回对应信息。")
    @PostMapping(value = "/idcard")
    @LimitPoint(name = "ocr_idcard_single", key = "ocr_idcard_single")
    public Object idcard(@ApiParam(value = "身份证图片文件", required = true) @RequestParam("file") MultipartFile file,
                        @ApiParam(value = "身份证类型：正面('front')或反面('back')", required = false) @RequestParam(value = "type", required = false) String type) {
        try {
            AuthUser currentUser = UserContext.getCurrentUser();
            String userId = currentUser != null ? currentUser.getId() : "anonymous";
            log.info("用户[{}]调用统一身份证识别服务，类型: {}", userId, type);

            if (file == null || file.isEmpty()) {
                return ResultUtil.error(ResultCode.FILE_NOT_EXIST_ERROR);
            }

            // 根据type参数决定识别正面还是反面
            if ("front".equals(type)) {
                return idcardFront(file);
            } else if ("back".equals(type)) {
                return idcardBack(file);
            }

            // 如果没有指定type或type不是front/back，尝试同时识别正反面
            IdCardInfo frontInfo = ocrService.recognizeIdCardFront(file);
            IdCardInfo backInfo = ocrService.recognizeIdCardBack(file);

            IdCardInfo mergedInfo = new IdCardInfo();
            boolean frontDataFound = false;
            boolean backDataFound = false;

            if (frontInfo != null) {
                if (frontInfo.getName() != null) {
                    mergedInfo.setName(frontInfo.getName()); frontDataFound = true;}
                if (frontInfo.getIdNumber() != null) {
                     mergedInfo.setIdNumber(frontInfo.getIdNumber()); frontDataFound = true;
                    }
                if (frontInfo.getGender() != null) mergedInfo.setGender(frontInfo.getGender());
                if (frontInfo.getEthnicity() != null) mergedInfo.setEthnicity(frontInfo.getEthnicity());
                if (frontInfo.getBirthDate() != null) mergedInfo.setBirthDate(frontInfo.getBirthDate());
                if (frontInfo.getAddress() != null) mergedInfo.setAddress(frontInfo.getAddress());
            }

            if (backInfo != null) {
                if (backInfo.getIssueAuthority() != null) { mergedInfo.setIssueAuthority(backInfo.getIssueAuthority()); backDataFound = true; }
                if (backInfo.getValidFrom() != null) { mergedInfo.setValidFrom(backInfo.getValidFrom()); backDataFound = true; }
                if (backInfo.getValidTo() != null) { mergedInfo.setValidTo(backInfo.getValidTo()); backDataFound = true; }
                if (backInfo.getValidPeriod() != null) { mergedInfo.setValidPeriod(backInfo.getValidPeriod()); backDataFound = true; }
                if (backInfo.getIsLongTerm() != null) { mergedInfo.setIsLongTerm(backInfo.getIsLongTerm()); }
                // 确保长期标志被正确设置
                mergedInfo.checkAndSetLongTerm();
            }

            Map<String, Object> resultData = new HashMap<>();
            if (mergedInfo.getName() != null) resultData.put("name", mergedInfo.getName());
            if (mergedInfo.getIdNumber() != null) resultData.put("idNumber", mergedInfo.getIdNumber());
            if (mergedInfo.getGender() != null) resultData.put("gender", mergedInfo.getGender());
            if (mergedInfo.getEthnicity() != null) resultData.put("ethnicity", mergedInfo.getEthnicity());
            if (mergedInfo.getBirthDate() != null) {
                resultData.put("birthDate", mergedInfo.getBirthDate());
                resultData.put("birthDateAuto", true);
            }
            if (mergedInfo.getAddress() != null) resultData.put("address", mergedInfo.getAddress());
            if (mergedInfo.getIssueAuthority() != null) resultData.put("issueAuthority", mergedInfo.getIssueAuthority());

            // 处理有效期限相关字段
            if (mergedInfo.getValidFrom() != null) resultData.put("validFrom", mergedInfo.getValidFrom());
            if (mergedInfo.getValidTo() != null) resultData.put("validTo", mergedInfo.getValidTo());
            if (mergedInfo.getIsLongTerm() != null) resultData.put("isLongTerm", mergedInfo.getIsLongTerm());
            if (mergedInfo.getValidPeriod() != null) {
                resultData.put("validPeriod", mergedInfo.getValidPeriod());
            } else if (mergedInfo.getValidFrom() != null && mergedInfo.getValidTo() != null) {
                resultData.put("validPeriod", mergedInfo.getValidFrom() + "-" + mergedInfo.getValidTo());
            }

            if (resultData.isEmpty() || (!frontDataFound && !backDataFound)) {
                 log.warn("身份证图片未能识别出任何关键信息，用户: {}", userId);
                 Map<String, Object> errorResponse = new HashMap<>();
                 errorResponse.put("success", false);
                 errorResponse.put("message", "身份证识别失败: 图片不清晰或非身份证图片");
                 errorResponse.put("code", ResultCode.ERROR.code());
                 errorResponse.put("result", null);
                 return errorResponse;
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "success");
            response.put("code", 200);
            response.put("result", resultData);

            return response;
        } catch (ServiceException e) {
            log.error("身份证识别业务异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证识别失败: " + e.getMessage());
            errorResponse.put("code", e.getResultCode() != null ? e.getResultCode().code() : ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        } catch (Exception e) {
            log.error("身份证识别系统异常", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证识别失败: 系统内部错误");
            errorResponse.put("code", ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        }
    }

    @ApiOperation(value = "营业执照识别")
    @PostMapping(value = "/business/license")
    @LimitPoint(name = "ocr_business_license", key = "ocr_business_license")
    public ResultMessage<Object> businessLicense(MultipartFile file) {
        try {
            logUserAction("营业执照识别");

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            BusinessLicenseInfo licenseInfo = ocrService.recognizeBusinessLicense(file);

            if (licenseInfo == null || !licenseInfo.isValid()) {
                return ResultMessage.builder()
                        .success(false)
                        .code(ResultCode.ERROR.code())
                        .message("营业执照识别失败: 图片不清晰或非营业执照图片")
                        .build();
            }

            return OcrResponseUtil.buildBusinessLicenseSuccess(licenseInfo);

        } catch (ServiceException e) {
            log.error("营业执照识别失败: {}", e.getMessage());
            return ResultMessage.builder()
                    .success(false)
                    .code(e.getResultCode() != null ? e.getResultCode().code() : ResultCode.ERROR.code())
                    .message("营业执照识别失败: " + e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("营业执照识别异常", e);
            return ResultMessage.builder()
                    .success(false)
                    .code(ResultCode.ERROR.code())
                    .message("营业执照识别失败: 系统内部错误")
                    .build();
        }
    }

    /**
     * 记录用户操作日志
     */
    private void logUserAction(String action) {
        AuthUser currentUser = UserContext.getCurrentUser();
        String userId = currentUser != null ? currentUser.getId() : "anonymous";
        log.info("用户[{}]调用{}", userId, action);
    }
}