package cn.lili.controller.common;

import cn.lili.cache.limit.annotation.LimitPoint;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.ocr.entity.dto.BusinessLicenseInfo;
import cn.lili.modules.ocr.entity.dto.IdCardInfo;
import cn.lili.modules.ocr.service.OcrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * OCR控制器
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Slf4j
@RestController
@Api(tags = "OCR身份证和营业执照识别接口")
@RequestMapping("/common/common/ocr")
public class OcrController {

    @Autowired
    private OcrService ocrService;

    @ApiOperation(value = "身份证正面识别", notes = "上传身份证正面图片，识别并返回对应信息")
    @PostMapping(value = "/idcard/front")
    @LimitPoint(name = "ocr_idcard_front", key = "ocr_idcard_front")
    public Object idcardFront(@ApiParam(value = "身份证正面图片文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            AuthUser currentUser = UserContext.getCurrentUser();
            String userId = currentUser != null ? currentUser.getId() : "anonymous";
            log.info("用户[{}]调用身份证正面识别服务", userId);

            if (file == null || file.isEmpty()) {
                return ResultUtil.error(ResultCode.FILE_NOT_EXIST_ERROR);
            }

            IdCardInfo idCardInfo = ocrService.recognizeIdCardFront(file);

            // 处理识别结果为空的情况
            if (idCardInfo == null || (idCardInfo.getName() == null && idCardInfo.getIdNumber() == null)) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "身份证正面识别失败: 图片不清晰或非身份证图片");
                errorResponse.put("code", ResultCode.ERROR.code());
                errorResponse.put("result", null);
                return errorResponse;
            }

            // 构造前端需要的结果格式
            Map<String, Object> resultData = new HashMap<>();
            if (idCardInfo.getName() != null) resultData.put("name", idCardInfo.getName());
            if (idCardInfo.getIdNumber() != null) resultData.put("idNumber", idCardInfo.getIdNumber());
            if (idCardInfo.getBirthDate() != null) {
                resultData.put("birthDate", idCardInfo.getBirthDate());
                // 明确指示此字段是自动识别的
                resultData.put("birthDateAuto", true);
            }
            if (idCardInfo.getGender() != null) resultData.put("gender", idCardInfo.getGender());
            if (idCardInfo.getAddress() != null) resultData.put("address", idCardInfo.getAddress());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "success");
            response.put("code", 200);
            response.put("result", resultData);
            
            return response;
        } catch (ServiceException e) {
            log.error("身份证正面识别业务异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证正面识别失败: " + e.getMessage());
            errorResponse.put("code", e.getResultCode() != null ? e.getResultCode().code() : ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        } catch (Exception e) {
            log.error("身份证正面识别系统异常", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证正面识别失败: 系统内部错误");
            errorResponse.put("code", ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        }
    }

    @ApiOperation(value = "身份证背面识别", notes = "上传身份证背面图片，识别并返回对应信息")
    @PostMapping(value = "/idcard/back")
    @LimitPoint(name = "ocr_idcard_back", key = "ocr_idcard_back")
    public Object idcardBack(@ApiParam(value = "身份证背面图片文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            AuthUser currentUser = UserContext.getCurrentUser();
            String userId = currentUser != null ? currentUser.getId() : "anonymous";
            log.info("用户[{}]调用身份证背面识别服务", userId);

            if (file == null || file.isEmpty()) {
                return ResultUtil.error(ResultCode.FILE_NOT_EXIST_ERROR);
            }

            IdCardInfo idCardInfo = ocrService.recognizeIdCardBack(file);

            // 处理识别结果为空的情况
            if (idCardInfo == null || (idCardInfo.getIssueAuthority() == null && idCardInfo.getValidPeriod() == null)) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "身份证背面识别失败: 图片不清晰或非身份证图片");
                errorResponse.put("code", ResultCode.ERROR.code());
                errorResponse.put("result", null);
                return errorResponse;
            }

            // 构造前端需要的结果格式
            Map<String, Object> resultData = new HashMap<>();
            if (idCardInfo.getIssueAuthority() != null) resultData.put("issueAuthority", idCardInfo.getIssueAuthority());
            if (idCardInfo.getValidFrom() != null) resultData.put("validFrom", idCardInfo.getValidFrom());
            if (idCardInfo.getValidTo() != null) resultData.put("validTo", idCardInfo.getValidTo());
            if (idCardInfo.getIsLongTerm() != null) resultData.put("isLongTerm", idCardInfo.getIsLongTerm());
            if (idCardInfo.getValidPeriod() != null) {
                resultData.put("validPeriod", idCardInfo.getValidPeriod());
            } else if (idCardInfo.getValidFrom() != null && idCardInfo.getValidTo() != null) {
                resultData.put("validPeriod", idCardInfo.getValidFrom() + "-" + idCardInfo.getValidTo());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "success");
            response.put("code", 200);
            response.put("result", resultData);
            
            return response;
        } catch (ServiceException e) {
            log.error("身份证背面识别业务异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证背面识别失败: " + e.getMessage());
            errorResponse.put("code", e.getResultCode() != null ? e.getResultCode().code() : ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        } catch (Exception e) {
            log.error("身份证背面识别系统异常", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证背面识别失败: 系统内部错误");
            errorResponse.put("code", ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        }
    }

    @ApiOperation(value = "身份证识别（单面）", notes = "上传身份证正面或反面图片，接口会尝试识别并返回对应信息。")
    @PostMapping(value = "/idcard")
    @LimitPoint(name = "ocr_idcard_single", key = "ocr_idcard_single")
    public Object idcard(@ApiParam(value = "身份证图片文件", required = true) @RequestParam("file") MultipartFile file,
                        @ApiParam(value = "身份证类型：正面('front')或反面('back')", required = false) @RequestParam(value = "type", required = false) String type) {
        try {
            AuthUser currentUser = UserContext.getCurrentUser();
            String userId = currentUser != null ? currentUser.getId() : "anonymous";
            log.info("用户[{}]调用统一身份证识别服务，类型: {}", userId, type);

            if (file == null || file.isEmpty()) {
                return ResultUtil.error(ResultCode.FILE_NOT_EXIST_ERROR);
            }

            // 根据type参数决定识别正面还是反面
            if ("front".equals(type)) {
                return idcardFront(file);
            } else if ("back".equals(type)) {
                return idcardBack(file);
            }

            // 如果没有指定type或type不是front/back，尝试同时识别正反面
            IdCardInfo frontInfo = ocrService.recognizeIdCardFront(file);
            IdCardInfo backInfo = ocrService.recognizeIdCardBack(file);

            IdCardInfo mergedInfo = new IdCardInfo();
            boolean frontDataFound = false;
            boolean backDataFound = false;

            if (frontInfo != null) {
                if (frontInfo.getName() != null) { 
                    mergedInfo.setName(frontInfo.getName()); frontDataFound = true;}
                if (frontInfo.getIdNumber() != null) {
                     mergedInfo.setIdNumber(frontInfo.getIdNumber()); frontDataFound = true;
                    }
                if (frontInfo.getGender() != null) mergedInfo.setGender(frontInfo.getGender());
                if (frontInfo.getEthnicity() != null) mergedInfo.setEthnicity(frontInfo.getEthnicity());
                if (frontInfo.getBirthDate() != null) mergedInfo.setBirthDate(frontInfo.getBirthDate());
                if (frontInfo.getAddress() != null) mergedInfo.setAddress(frontInfo.getAddress());
            }
            
            if (backInfo != null) {
                if (backInfo.getIssueAuthority() != null) { mergedInfo.setIssueAuthority(backInfo.getIssueAuthority()); backDataFound = true; }
                if (backInfo.getValidFrom() != null) { mergedInfo.setValidFrom(backInfo.getValidFrom()); backDataFound = true; }
                if (backInfo.getValidTo() != null) { mergedInfo.setValidTo(backInfo.getValidTo()); backDataFound = true; }
                if (backInfo.getValidPeriod() != null) { mergedInfo.setValidPeriod(backInfo.getValidPeriod()); backDataFound = true; }
                if (backInfo.getIsLongTerm() != null) { mergedInfo.setIsLongTerm(backInfo.getIsLongTerm()); }
                // 确保长期标志被正确设置
                mergedInfo.checkAndSetLongTerm();
            }

            Map<String, Object> resultData = new HashMap<>();
            if (mergedInfo.getName() != null) resultData.put("name", mergedInfo.getName());
            if (mergedInfo.getIdNumber() != null) resultData.put("idNumber", mergedInfo.getIdNumber());
            if (mergedInfo.getGender() != null) resultData.put("gender", mergedInfo.getGender());
            if (mergedInfo.getEthnicity() != null) resultData.put("ethnicity", mergedInfo.getEthnicity());
            if (mergedInfo.getBirthDate() != null) {
                resultData.put("birthDate", mergedInfo.getBirthDate());
                resultData.put("birthDateAuto", true);
            }
            if (mergedInfo.getAddress() != null) resultData.put("address", mergedInfo.getAddress());
            if (mergedInfo.getIssueAuthority() != null) resultData.put("issueAuthority", mergedInfo.getIssueAuthority());
            
            // 处理有效期限相关字段
            if (mergedInfo.getValidFrom() != null) resultData.put("validFrom", mergedInfo.getValidFrom());
            if (mergedInfo.getValidTo() != null) resultData.put("validTo", mergedInfo.getValidTo());
            if (mergedInfo.getIsLongTerm() != null) resultData.put("isLongTerm", mergedInfo.getIsLongTerm());
            if (mergedInfo.getValidPeriod() != null) {
                resultData.put("validPeriod", mergedInfo.getValidPeriod());
            } else if (mergedInfo.getValidFrom() != null && mergedInfo.getValidTo() != null) {
                resultData.put("validPeriod", mergedInfo.getValidFrom() + "-" + mergedInfo.getValidTo());
            }

            if (resultData.isEmpty() || (!frontDataFound && !backDataFound)) {
                 log.warn("身份证图片未能识别出任何关键信息，用户: {}", userId);
                 Map<String, Object> errorResponse = new HashMap<>();
                 errorResponse.put("success", false);
                 errorResponse.put("message", "身份证识别失败: 图片不清晰或非身份证图片");
                 errorResponse.put("code", ResultCode.ERROR.code());
                 errorResponse.put("result", null);
                 return errorResponse;
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "success");
            response.put("code", 200);
            response.put("result", resultData);
            
            return response;
        } catch (ServiceException e) {
            log.error("身份证识别业务异常: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证识别失败: " + e.getMessage());
            errorResponse.put("code", e.getResultCode() != null ? e.getResultCode().code() : ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        } catch (Exception e) {
            log.error("身份证识别系统异常", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "身份证识别失败: 系统内部错误");
            errorResponse.put("code", ResultCode.ERROR.code());
            errorResponse.put("result", null);
            return errorResponse;
        }
    }

    @ApiOperation(value = "营业执照识别")
    @PostMapping(value = "/business/license")
    @LimitPoint(name = "ocr_business_license", key = "ocr_business_license")
    public Object businessLicense(MultipartFile file) {
        try {
            // 获取当前登录的用户
            AuthUser currentUser = UserContext.getCurrentUser();
            String userId = currentUser != null ? currentUser.getId() : "anonymous";
            log.info("用户[{}]调用营业执照识别服务", userId);
            
            // 检查上传的文件
            if (file == null || file.isEmpty()) {
                return ResultUtil.error(ResultCode.FILE_NOT_EXIST_ERROR);
            }
            
            // 调用识别服务获取原始识别结果
            BusinessLicenseInfo ocrResult = ocrService.recognizeBusinessLicense(file);
            
            // 创建结果映射
            Map<String, Object> result = new HashMap<>();
            
            // 映射字段 (OCR字段 -> API字段)
            result.put("companyName", ocrResult.getCompanyName());  
            result.put("licenseNum", ocrResult.getLicenseNum());
            result.put("legalPerson", ocrResult.getLegalPerson());
            result.put("businessScope", ocrResult.getBusinessScope());
            result.put("address", ocrResult.getAddress());
            
            // 处理注册资本，提取数字部分
            String registeredCapital = ocrResult.getRegisteredCapital();
            if (registeredCapital != null) {
                String capitalValue = extractNumber(registeredCapital);
                result.put("registeredCapital", capitalValue);
            } else {
                result.put("registeredCapital", "");
            }
            
            // 直接构建前端期望的返回格式
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "success");
            response.put("code", 200);
            response.put("result", result);
            
            return response;
        } catch (ServiceException e) {
            log.error("营业执照识别失败: {}", e.getMessage());
            return ResultUtil.error(e.getResultCode());
        } catch (Exception e) {
            log.error("营业执照识别异常", e);
            return ResultUtil.error(ResultCode.ERROR.code(), "营业执照识别失败：" + e.getMessage());
        }
    }
    
    /**
     * 从文本中提取数字
     * @param text 包含数字的文本
     * @return 提取的数字
     */
    private String extractNumber(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        
        // 使用正则表达式提取数字部分
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?");
        Matcher matcher = pattern.matcher(text);
        
        if (matcher.find()) {
            return matcher.group();
        }
        
        return "";
    }
} 