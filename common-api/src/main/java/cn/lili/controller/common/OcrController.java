package cn.lili.controller.common;

import cn.lili.cache.limit.annotation.LimitPoint;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.OcrResponseUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.ocr.entity.dto.BusinessLicenseInfo;
import cn.lili.modules.ocr.entity.dto.IdCardInfo;
import cn.lili.modules.ocr.service.OcrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * OCR控制器
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Slf4j
@RestController
@Api(tags = "OCR身份证和营业执照识别接口")
@RequestMapping("/common/common/ocr")
public class OcrController {

    @Autowired
    private OcrService ocrService;

    @ApiOperation(value = "身份证正面识别", notes = "上传身份证正面图片，识别并返回对应信息")
    @PostMapping(value = "/idcard/front")
    @LimitPoint(name = "ocr_idcard_front", key = "ocr_idcard_front")
    public Object idcardFront(@ApiParam(value = "身份证正面图片文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            logUserAction("身份证正面识别");

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            IdCardInfo idCardInfo = ocrService.recognizeIdCardFront(file);

            if (idCardInfo == null || !idCardInfo.isValidFront()) {
                return OcrResponseUtil.buildFailureResponse("身份证正面识别失败: 图片不清晰或非身份证图片");
            }

            return OcrResponseUtil.buildIdCardFrontSuccess(idCardInfo);

        } catch (ServiceException e) {
            log.error("身份证正面识别业务异常: {}", e.getMessage(), e);
            return OcrResponseUtil.buildFailureResponse("身份证正面识别失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("身份证正面识别系统异常", e);
            return OcrResponseUtil.buildFailureResponse("身份证正面识别失败: 系统内部错误");
        }
    }

    @ApiOperation(value = "身份证背面识别", notes = "上传身份证背面图片，识别并返回对应信息")
    @PostMapping(value = "/idcard/back")
    @LimitPoint(name = "ocr_idcard_back", key = "ocr_idcard_back")
    public Object idcardBack(@ApiParam(value = "身份证背面图片文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            logUserAction("身份证背面识别");

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            IdCardInfo idCardInfo = ocrService.recognizeIdCardBack(file);

            if (idCardInfo == null || !idCardInfo.isValidBack()) {
                return OcrResponseUtil.buildFailureResponse("身份证背面识别失败: 图片不清晰或非身份证图片");
            }

            return OcrResponseUtil.buildIdCardBackSuccess(idCardInfo);

        } catch (ServiceException e) {
            log.error("身份证背面识别业务异常: {}", e.getMessage(), e);
            return OcrResponseUtil.buildFailureResponse("身份证背面识别失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("身份证背面识别系统异常", e);
            return OcrResponseUtil.buildFailureResponse("身份证背面识别失败: 系统内部错误");
        }
    }

    @ApiOperation(value = "身份证识别（智能）", notes = "上传身份证图片，自动识别正面或反面并返回对应信息")
    @PostMapping(value = "/idcard")
    @LimitPoint(name = "ocr_idcard_single", key = "ocr_idcard_single")
    public Object idcard(@ApiParam(value = "身份证图片文件", required = true) @RequestParam("file") MultipartFile file,
                        @ApiParam(value = "身份证类型：正面('front')或反面('back')", required = false) @RequestParam(value = "type", required = false) String type) {
        try {
            logUserAction("身份证智能识别，类型: " + type);

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            // 根据type参数决定识别正面还是反面
            if ("front".equals(type)) {
                return idcardFront(file);
            } else if ("back".equals(type)) {
                return idcardBack(file);
            }

            // 智能识别：先尝试正面，再尝试背面
            IdCardInfo frontInfo = ocrService.recognizeIdCardFront(file);
            if (frontInfo != null && frontInfo.isValidFront()) {
                return OcrResponseUtil.buildIdCardFrontSuccess(frontInfo);
            }

            IdCardInfo backInfo = ocrService.recognizeIdCardBack(file);
            if (backInfo != null && backInfo.isValidBack()) {
                return OcrResponseUtil.buildIdCardBackSuccess(backInfo);
            }

            return OcrResponseUtil.buildFailureResponse("身份证识别失败: 图片不清晰或非身份证图片");

        } catch (ServiceException e) {
            log.error("身份证识别业务异常: {}", e.getMessage(), e);
            return OcrResponseUtil.buildFailureResponse("身份证识别失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("身份证识别系统异常", e);
            return OcrResponseUtil.buildFailureResponse("身份证识别失败: 系统内部错误");
        }
    }

    @ApiOperation(value = "营业执照识别")
    @PostMapping(value = "/business/license")
    @LimitPoint(name = "ocr_business_license", key = "ocr_business_license")
    public ResultMessage<Map<String, Object>> businessLicense(MultipartFile file) {
        try {
            logUserAction("营业执照识别");

            if (file == null || file.isEmpty()) {
                return OcrResponseUtil.buildFileErrorResponse();
            }

            BusinessLicenseInfo licenseInfo = ocrService.recognizeBusinessLicense(file);

            if (licenseInfo == null || !licenseInfo.isValid()) {
                ResultMessage<Map<String, Object>> result = new ResultMessage<>();
                result.setSuccess(false);
                result.setCode(ResultCode.ERROR.code());
                result.setMessage("营业执照识别失败: 图片不清晰或非营业执照图片");
                return result;
            }

            return OcrResponseUtil.buildBusinessLicenseSuccess(licenseInfo);

        } catch (ServiceException e) {
            log.error("营业执照识别失败: {}", e.getMessage());
            ResultMessage<Map<String, Object>> result = new ResultMessage<>();
            result.setSuccess(false);
            result.setCode(e.getResultCode() != null ? e.getResultCode().code() : ResultCode.ERROR.code());
            result.setMessage("营业执照识别失败: " + e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("营业执照识别异常", e);
            ResultMessage<Map<String, Object>> result = new ResultMessage<>();
            result.setSuccess(false);
            result.setCode(ResultCode.ERROR.code());
            result.setMessage("营业执照识别失败: 系统内部错误");
            return result;
        }
    }

    /**
     * 记录用户操作日志
     */
    private void logUserAction(String action) {
        AuthUser currentUser = UserContext.getCurrentUser();
        String userId = currentUser != null ? currentUser.getId() : "anonymous";
        log.info("用户[{}]调用{}", userId, action);
    }
}