package cn.lili.controller.common;

import cn.lili.cache.limit.annotation.LimitPoint;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.sms.SmsUtil;
import cn.lili.modules.verification.entity.enums.VerificationEnums;
import cn.lili.modules.verification.service.VerificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.exception.ServiceException;

/**
 * 短信验证码接口
 *
 * <AUTHOR>
 * @since 2020/11/26 15:41
 */
@RestController
@Api(tags = "短信验证码接口")
@RequestMapping("/common/common/sms")
public class SmsController {

    // private static final Logger log = LoggerFactory.getLogger(SmsController.class); // Commenting out logger if not used elsewhere, or remove if imports are also removed

    @Autowired
    private SmsUtil smsUtil;
    @Autowired
    private VerificationService verificationService;

    @LimitPoint(name = "sms_send", key = "sms")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "String", name = "mobile", value = "手机号"),
            @ApiImplicitParam(paramType = "header", dataType = "String", name = "uuid", value = "uuid", required = false)
    })
    @GetMapping("/{verificationEnums}/{mobile}")
    @ApiOperation(value = "发送短信验证码,一分钟同一个ip请求1次")
    public ResultMessage getSmsCode(
            @RequestHeader(required = false) String uuid,
            @PathVariable String mobile,
            @PathVariable VerificationEnums verificationEnums) {

        if (verificationEnums != VerificationEnums.BIND_NEW_PHONE) {
            if (CharSequenceUtil.isBlank(uuid)) { 
                // log.warn("[SmsController] UUID is blank for a verification-required enum: {}", verificationEnums);
                 throw new ServiceException(ResultCode.PARAMS_ERROR, "图形验证码标识不能为空");
            }
            // log.info("[SmsController] Performing verificationService.check for UUID: {}, Enums: {}", uuid, verificationEnums);
            verificationService.check(uuid, verificationEnums);
        } else {
            if (CharSequenceUtil.isBlank(uuid)) {
                // log.warn("[SmsController] UUID is blank for BIND_NEW_PHONE. This might affect SMS code caching if not handled by smsUtil. Front-end should pass the UUID from the previous step (old phone verification).");
            }
            // log.info("[SmsController] Skipping verificationService.check for BIND_NEW_PHONE. UUID received: {}", uuid);
        }

        smsUtil.sendSmsCode(mobile, verificationEnums, uuid);
        return ResultUtil.success(ResultCode.VERIFICATION_SEND_SUCCESS);
    }
}
