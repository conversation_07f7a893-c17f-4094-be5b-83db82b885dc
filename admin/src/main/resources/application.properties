# åºç¨ç¨åºåç§°
spring.application.name=SpringBootAdmin
# åºç¨ç¨åºç«¯å£
server.port=8000
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
#è´¦å·å¯ç 
spring.security.user.name=admin
spring.security.user.password=admin
spring.mail.host=smtp.qq.com
# toåfromé½è¦éç½®ï¼å¦ååéé®ä»¶æ¶ä¼æ¥é
spring.boot.admin.notify.mail.to=<EMAIL>
spring.boot.admin.notify.mail.from=<EMAIL>
# é®ä»¶çç¨æ·ååå¯ç 
spring.mail.username=<EMAIL>
spring.mail.password=abcdefg123456!@#$%^
# æ¥å¿æä»¶è·¯å¾
logging.file.path=lili-logs/admin
lili.data.logstash.server=***********:4560
# æä»¶æ ¼å¼
logging.pattern.file=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx
