# OCR配置管理功能实现文档

## 📋 功能概述

本文档描述了 OCR（光学字符识别）配置管理功能的前端实现需求。该功能允许管理员在后台系统中配置 OCR 服务的相关参数，包括阿里云 AccessKey、区域设置等，实现与短信配置相同的管理模式。

## 🎯 功能目标

- 提供可视化的 OCR 配置管理界面
- 支持 OCR 服务的启用/禁用控制
- 安全地管理敏感的认证信息
- 与现有系统设置保持一致的用户体验

## 🔧 后端接口说明

### 1. 获取 OCR 配置
```http
GET /manager/setting/setting/get/OCR_SETTING
```

**响应示例：**
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "result": {
    "accessKeyId": "LTAI5tK1vFaCfNUG9bNRbvos",
    "accessKeySecret": "******************************",
    "regionId": "cn-hangzhou",
    "enabled": true,
    "type": "ALIYUN"
  }
}
```

### 2. 更新 OCR 配置
```http
PUT /manager/setting/setting/put/OCR_SETTING
Content-Type: application/json
```

**请求体：**
```json
{
  "accessKeyId": "您的阿里云AccessKeyId",
  "accessKeySecret": "您的阿里云AccessKeySecret",
  "regionId": "cn-hangzhou",
  "enabled": true,
  "type": "ALIYUN"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "success",
  "code": 200
}
```

## 📱 前端实现需求

### 1. 页面位置
- **路径**：系统设置 → OCR设置
- **菜单位置**：与短信设置、OSS设置等并列
- **页面标题**：OCR识别设置

### 2. 页面布局要求

#### 2.1 页面结构
```
┌─────────────────────────────────────┐
│ OCR识别设置                          │
├─────────────────────────────────────┤
│ [启用OCR服务] ☑️                     │
│                                     │
│ 服务类型：[阿里云OCR ▼]              │
│                                     │
│ AccessKey ID：                      │
│ [________________________]          │
│                                     │
│ AccessKey Secret：                  │
│ [________________________] [👁️]     │
│                                     │
│ 区域设置：                          │
│ [cn-hangzhou ▼]                     │
│                                     │
│ [保存配置] [测试连接]                │
└─────────────────────────────────────┘
```

#### 2.2 表单字段详细说明

**1. 启用OCR服务**
- **组件类型**：Switch 开关
- **字段名**：`enabled`
- **默认值**：`true`
- **说明**：控制是否启用OCR识别功能

**2. 服务类型**
- **组件类型**：Select 下拉选择
- **字段名**：`type`
- **选项**：
  - `ALIYUN` - 阿里云OCR
- **默认值**：`ALIYUN`
- **说明**：当前仅支持阿里云，预留扩展其他服务商

**3. AccessKey ID**
- **组件类型**：Input 输入框
- **字段名**：`accessKeyId`
- **验证规则**：
  - 必填项
  - 长度：16-30位
  - 格式：字母数字组合
- **占位符**：请输入阿里云AccessKey ID
- **说明文字**：用于API认证的访问密钥ID

**4. AccessKey Secret**
- **组件类型**：Password 密码输入框
- **字段名**：`accessKeySecret`
- **验证规则**：
  - 必填项
  - 长度：30-50位
- **占位符**：请输入阿里云AccessKey Secret
- **功能要求**：
  - 默认隐藏显示（显示为 ****）
  - 提供显示/隐藏切换按钮
- **说明文字**：用于API认证的访问密钥，请妥善保管

**5. 区域设置**
- **组件类型**：Select 下拉选择
- **字段名**：`regionId`
- **选项**：
  ```javascript
  [
    { value: 'cn-hangzhou', label: '华东1（杭州）' },
    { value: 'cn-shanghai', label: '华东2（上海）' },
    { value: 'cn-beijing', label: '华北2（北京）' },
    { value: 'cn-shenzhen', label: '华南1（深圳）' },
    { value: 'cn-guangzhou', label: '华南2（广州）' }
  ]
  ```
- **默认值**：`cn-hangzhou`
- **说明文字**：选择阿里云OCR服务的区域节点

### 3. 交互功能要求

#### 3.1 表单验证
```javascript
// 验证规则示例
const validationRules = {
  accessKeyId: [
    { required: true, message: '请输入AccessKey ID' },
    { min: 16, max: 30, message: 'AccessKey ID长度应为16-30位' },
    { pattern: /^[A-Za-z0-9]+$/, message: 'AccessKey ID只能包含字母和数字' }
  ],
  accessKeySecret: [
    { required: true, message: '请输入AccessKey Secret' },
    { min: 30, max: 50, message: 'AccessKey Secret长度应为30-50位' }
  ],
  regionId: [
    { required: true, message: '请选择区域' }
  ]
}
```

#### 3.2 保存配置功能
- **触发条件**：点击"保存配置"按钮
- **前置验证**：表单验证通过
- **请求处理**：
  ```javascript
  // 保存逻辑示例
  const saveConfig = async (formData) => {
    try {
      // 显示加载状态
      setLoading(true);

      // 发送请求
      const response = await api.put('/manager/setting/setting/put/OCR_SETTING', formData);

      if (response.success) {
        // 显示成功提示
        message.success('OCR配置保存成功');
        // 可选：刷新页面数据
        loadConfig();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  ```

#### 3.3 测试连接功能（可选实现）
- **按钮位置**：保存按钮旁边
- **功能说明**：验证配置的有效性
- **实现方式**：调用一个简单的OCR测试接口
- **用户反馈**：显示连接成功/失败的提示

#### 3.4 数据加载
```javascript
// 页面初始化加载配置
const loadConfig = async () => {
  try {
    const response = await api.get('/manager/setting/setting/get/OCR_SETTING');
    if (response.success) {
      // 设置表单默认值
      form.setFieldsValue(response.result);
    }
  } catch (error) {
    console.error('加载OCR配置失败:', error);
  }
};
```

### 4. 样式和用户体验要求

#### 4.1 样式规范
- **整体风格**：与现有系统设置页面保持一致
- **表单布局**：使用栅格系统，标签宽度统一
- **间距规范**：字段间距 16px，区块间距 24px
- **颜色规范**：遵循系统主题色彩

#### 4.2 响应式设计
- **桌面端**：表单宽度最大 600px
- **平板端**：自适应宽度，保持良好的可读性
- **移动端**：单列布局，按钮全宽显示

#### 4.3 用户体验细节
- **加载状态**：保存时显示 loading 效果
- **错误提示**：字段级别的实时验证提示
- **成功反馈**：保存成功后的明确提示
- **帮助信息**：关键字段提供帮助说明

### 5. 安全性要求

#### 5.1 敏感信息处理
- **AccessKey Secret**：
  - 默认以密码形式显示
  - 提供显示/隐藏切换功能
  - 编辑时不显示原值，需重新输入

#### 5.2 权限控制
- **页面访问**：仅管理员可访问
- **操作权限**：需要相应的系统设置权限

### 6. 错误处理

#### 6.1 常见错误场景
```javascript
// 错误处理示例
const errorMessages = {
  400: '请求参数错误，请检查输入信息',
  401: '权限不足，请联系管理员',
  403: '禁止访问，请检查权限设置',
  500: '服务器内部错误，请稍后重试',
  network: '网络连接失败，请检查网络设置'
};
```

#### 6.2 用户友好提示
- **网络错误**：提供重试机制
- **验证失败**：高亮错误字段，显示具体错误信息
- **保存失败**：保留用户输入，避免数据丢失

### 7. 测试要点

#### 7.1 功能测试
- [ ] 页面正常加载和数据显示
- [ ] 表单验证规则正确执行
- [ ] 保存功能正常工作
- [ ] 错误处理机制有效
- [ ] 权限控制正确

#### 7.2 兼容性测试
- [ ] 主流浏览器兼容性
- [ ] 不同屏幕尺寸适配
- [ ] 移动端操作体验

## 📝 开发注意事项

1. **代码复用**：参考现有的短信设置页面实现，保持代码风格一致
2. **国际化**：预留多语言支持的文本标识
3. **日志记录**：关键操作添加前端日志记录
4. **性能优化**：合理使用缓存，避免频繁请求
5. **文档更新**：完成后更新相关的用户手册

## 🔗 参考资料

- **现有页面参考**：系统设置 → 短信设置
- **UI组件库**：使用项目统一的组件库
- **API文档**：参考系统设置相关接口文档
- **设计规范**：遵循项目的UI设计规范

---

**注意**：本文档描述的是前端实现需求，后端接口已经完成开发和测试。前端开发完成后，请进行充分的功能测试，确保与后端接口的正确对接。
