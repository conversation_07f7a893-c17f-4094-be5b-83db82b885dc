package cn.lili.common.utils;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.ocr.entity.dto.BusinessLicenseInfo;
import cn.lili.modules.ocr.entity.dto.IdCardInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * OCR响应工具类
 * 统一处理OCR识别结果的响应格式
 *
 * <AUTHOR>
 * @since 2024/01/01
 */
public class OcrResponseUtil {

    /**
     * 构建身份证正面识别成功响应
     */
    public static Map<String, Object> buildIdCardFrontSuccess(IdCardInfo idCardInfo) {
        Map<String, Object> resultData = new HashMap<>();
        
        // 只添加非空字段
        addIfNotNull(resultData, "name", idCardInfo.getName());
        addIfNotNull(resultData, "idNumber", idCardInfo.getIdNumber());
        addIfNotNull(resultData, "gender", idCardInfo.getGender());
        addIfNotNull(resultData, "address", idCardInfo.getAddress());
        
        if (idCardInfo.getBirthDate() != null) {
            resultData.put("birthDate", idCardInfo.getBirthDate());
            resultData.put("birthDateAuto", true);
        }
        
        return buildSuccessResponse(resultData);
    }

    /**
     * 构建身份证背面识别成功响应
     */
    public static Map<String, Object> buildIdCardBackSuccess(IdCardInfo idCardInfo) {
        Map<String, Object> resultData = new HashMap<>();
        
        addIfNotNull(resultData, "issueAuthority", idCardInfo.getIssueAuthority());
        addIfNotNull(resultData, "validFrom", idCardInfo.getValidFrom());
        addIfNotNull(resultData, "validTo", idCardInfo.getValidTo());
        addIfNotNull(resultData, "validPeriod", idCardInfo.getValidPeriod());
        
        if (idCardInfo.getIsLongTerm() != null) {
            resultData.put("isLongTerm", idCardInfo.getIsLongTerm());
        }
        
        return buildSuccessResponse(resultData);
    }

    /**
     * 构建营业执照识别成功响应
     */
    public static ResultMessage<Map<String, Object>> buildBusinessLicenseSuccess(BusinessLicenseInfo licenseInfo) {
        Map<String, Object> resultData = new HashMap<>();
        
        addIfNotNull(resultData, "companyName", licenseInfo.getCompanyName());
        addIfNotNull(resultData, "licenseNum", licenseInfo.getLicenseNum());
        addIfNotNull(resultData, "legalPerson", licenseInfo.getLegalPerson());
        addIfNotNull(resultData, "businessScope", licenseInfo.getBusinessScope());
        addIfNotNull(resultData, "address", licenseInfo.getAddress());
        addIfNotNull(resultData, "establishDate", licenseInfo.getEstablishDate());
        addIfNotNull(resultData, "businessTerm", licenseInfo.getBusinessTerm());
        addIfNotNull(resultData, "companyType", licenseInfo.getCompanyType());
        
        // 处理注册资本，提取数字部分
        String capitalNumber = licenseInfo.getRegisteredCapitalNumber();
        resultData.put("registeredCapital", capitalNumber);
        
        return ResultMessage.<Map<String, Object>>builder()
                .success(true)
                .code(ResultCode.SUCCESS.code())
                .message("success")
                .result(resultData)
                .build();
    }

    /**
     * 构建识别失败响应
     */
    public static Map<String, Object> buildFailureResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("code", ResultCode.ERROR.code());
        response.put("result", null);
        return response;
    }

    /**
     * 构建文件错误响应
     */
    public static ResultMessage<Object> buildFileErrorResponse() {
        return ResultMessage.builder()
                .success(false)
                .code(ResultCode.FILE_NOT_EXIST_ERROR.code())
                .message("文件不存在或为空")
                .build();
    }

    /**
     * 构建成功响应的通用方法
     */
    private static Map<String, Object> buildSuccessResponse(Map<String, Object> resultData) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "success");
        response.put("code", 200);
        response.put("result", resultData);
        return response;
    }

    /**
     * 添加非空值到Map中
     */
    private static void addIfNotNull(Map<String, Object> map, String key, Object value) {
        if (value != null && !value.toString().trim().isEmpty()) {
            map.put(key, value);
        }
    }
}
