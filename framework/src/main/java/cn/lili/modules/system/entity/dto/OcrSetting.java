package cn.lili.modules.system.entity.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * OCR识别配置
 * 用于配置阿里云OCR服务的相关参数
 *
 * <AUTHOR>
 * @since 2024/01/01
 */
@Data
public class OcrSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 阿里云AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * 区域ID，默认为cn-hangzhou
     */
    private String regionId = "cn-hangzhou";

    /**
     * 是否启用OCR服务
     */
    private Boolean enabled = true;

    /**
     * OCR服务类型（预留字段，支持扩展其他服务商）
     */
    private String type = "ALIYUN";
}
