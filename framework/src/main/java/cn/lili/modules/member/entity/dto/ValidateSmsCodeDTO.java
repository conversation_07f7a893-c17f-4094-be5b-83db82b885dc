package cn.lili.modules.member.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 短信验证码校验DTO
 */
@Data
public class ValidateSmsCodeDTO {

    @NotBlank(message = "手机号码不能为空")
    @ApiModelProperty(value = "手机号码", required = true)
    private String mobile;

    @NotBlank(message = "短信验证码不能为空")
    @ApiModelProperty(value = "短信验证码", required = true)
    private String code;

    @NotBlank(message = "验证码类型不能为空")
    @ApiModelProperty(value = "验证码业务枚举 (例如 VERIFY_OLD_PHONE)", required = true)
    private String verificationEnums;
} 