package cn.lili.modules.ocr.entity.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 营业执照识别结果
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Data
@ApiModel(value = "营业执照识别结果")
public class BusinessLicenseInfo {

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String licenseNum;

    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    @ApiModelProperty(value = "注册资本")
    private String registeredCapital;

    @ApiModelProperty(value = "成立日期")
    private String establishDate;

    @ApiModelProperty(value = "营业期限")
    private String businessTerm;

    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    @ApiModelProperty(value = "企业地址")
    private String address;

    @ApiModelProperty(value = "企业类型")
    private String companyType;

    /**
     * 检查营业执照信息是否有效
     */
    public boolean isValid() {
        return StrUtil.isNotBlank(companyName) || StrUtil.isNotBlank(licenseNum);
    }

    /**
     * 提取注册资本中的数字部分
     */
    public String getRegisteredCapitalNumber() {
        if (StrUtil.isBlank(registeredCapital)) {
            return "";
        }

        // 使用正则表达式提取数字和小数点
        Pattern pattern = Pattern.compile("([0-9]+\\.?[0-9]*)");
        Matcher matcher = pattern.matcher(registeredCapital);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return "";
    }
}