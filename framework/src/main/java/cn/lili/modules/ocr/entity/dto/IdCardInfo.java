package cn.lili.modules.ocr.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 身份证识别结果
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Data
@ApiModel(value = "身份证识别结果")
public class IdCardInfo {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "民族")
    private String ethnicity;

    @ApiModelProperty(value = "出生日期", example = "1990-01-01", notes = "从身份证自动识别，格式为yyyy-MM-dd")
    private String birthDate;

    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "身份证号码")
    private String idNumber;

    @ApiModelProperty(value = "签发机关")
    private String issueAuthority;

    @ApiModelProperty(value = "有效期起", example = "2020-01-01", notes = "格式为yyyy-MM-dd")
    private String validFrom;

    @ApiModelProperty(value = "有效期止", example = "2030-01-01 或 长期", notes = "格式为yyyy-MM-dd或'长期'字符串")
    private String validTo;

    @ApiModelProperty(value = "完整有效期限", example = "2020-01-01-2030-01-01 或 2020-01-01-长期")
    private String validPeriod;
    
    @ApiModelProperty(value = "是否长期有效", example = "true", notes = "true表示长期有效，false表示有截止日期")
    private Boolean isLongTerm;
    
    /**
     * 判断并设置是否为长期有效证件
     */
    public void checkAndSetLongTerm() {
        if (this.validTo != null && ("长期".equals(this.validTo) || "长期有效".equals(this.validTo))) {
            this.isLongTerm = true;
        } else {
            this.isLongTerm = false;
        }
    }
} 