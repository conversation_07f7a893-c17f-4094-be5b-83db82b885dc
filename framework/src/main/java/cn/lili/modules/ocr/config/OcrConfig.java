package cn.lili.modules.ocr.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * OCR配置类
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "lili.ocr")
public class OcrConfig {

    /**
     * 阿里云AccessKey ID
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKey Secret
     */
    private String accessKeySecret;

    /**
     * 区域ID，默认为cn-shanghai
     */
    private String regionId = "cn-shanghai";

} 