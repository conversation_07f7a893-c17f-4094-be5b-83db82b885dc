package cn.lili.modules.ocr.serviceimpl;

import cn.hutool.core.util.StrUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.ocr.entity.dto.BusinessLicenseInfo;
import cn.lili.modules.ocr.entity.dto.IdCardInfo;
import cn.lili.modules.ocr.service.OcrService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.io.ByteArrayInputStream;

/**
 * OCR服务实现类
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Slf4j
@Service
public class OcrServiceImpl implements OcrService {

    @Value("${lili.ocr.accessKeyId}")
    private String accessKeyId;

    @Value("${lili.ocr.accessKeySecret}")
    private String accessKeySecret;

    private Client client;

    @PostConstruct
    public void init() throws Exception {
        log.info("初始化OCR客户端，accessKeyId是否为空: {}, accessKeySecret是否为空: {}", 
                 StrUtil.isEmpty(this.accessKeyId), StrUtil.isEmpty(this.accessKeySecret));
        
        if (StrUtil.isEmpty(this.accessKeyId) || StrUtil.isEmpty(this.accessKeySecret)) {
            log.error("OCR认证信息未正确配置！请检查application.yml中的lili.ocr配置项");
            return;
        }
        
        Config config = new Config()
                .setAccessKeyId(this.accessKeyId)
                .setAccessKeySecret(this.accessKeySecret);
        // Endpoint 请根据实际情况设置，文档中提到 cn-hangzhou
        config.endpoint = "ocr-api.cn-hangzhou.aliyuncs.com";
        this.client = new Client(config);
        
        log.info("OCR客户端初始化成功");
    }

    /**
     * 识别身份证正面
     *
     * @param file 身份证正面图片
     * @return 身份证信息
     */
    @Override
    public IdCardInfo recognizeIdCardFront(MultipartFile file) {
        try {
            if (this.client == null) {
                throw new ServiceException(ResultCode.ERROR, "OCR服务未正确初始化，请检查配置");
            }
            
            String base64Image = Base64.getEncoder().encodeToString(file.getBytes());

            // 根据SDK概述文档示例，使用RecognizeAllTextRequest并通过Type区分
            RecognizeAllTextRequest recognizeAllTextRequest = new RecognizeAllTextRequest()
                    .setType("IdCard")
                    .setBody(new ByteArrayInputStream(file.getBytes()));

            RuntimeOptions runtime = new RuntimeOptions();

            // 调用SDK进行识别
            RecognizeAllTextResponse response = this.client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);
            // log.info("身份证正面识别结果: {}", JSON.toJSONString(response.getBody()));

            // 解析SDK返回结果 - 根据SDK概述文档示例结构调整
            IdCardInfo idCardInfo = new IdCardInfo();
            if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyData data = response.getBody().getData();
                // 示例结构: Data -> SubImages[] -> KvInfo -> Data
                 if (data.getSubImages() != null && !data.getSubImages().isEmpty()) {
                    RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages subImage = data.getSubImages().get(0);
                    if (subImage.getKvInfo() != null && subImage.getKvInfo().getData() != null) {
                        JSONObject faceInfo = JSON.parseObject(JSON.toJSONString(subImage.getKvInfo().getData()));
                         if (faceInfo != null) {
                            idCardInfo.setName(faceInfo.getString("name"));
                            idCardInfo.setGender(faceInfo.getString("gender")); // DTO field is gender, SDK key is gender
                            idCardInfo.setEthnicity(faceInfo.getString("nationality")); // DTO field is ethnicity, SDK key is nationality
                            idCardInfo.setBirthDate(faceInfo.getString("birthDate")); 
                            idCardInfo.setAddress(faceInfo.getString("address"));
                            idCardInfo.setIdNumber(faceInfo.getString("idNumber")); // DTO field is idNumber, SDK key is idNumber
                        }
                    }
                }
            } else {
                 // 处理SDK返回错误
                 String errorCode = response != null && response.getBody() != null && response.getBody().getCode() != null ? response.getBody().getCode() : "UNKNOWN_SDK_ERROR";
                 String errorMessage = response != null && response.getBody() != null && response.getBody().getMessage() != null ? response.getBody().getMessage() : "身份证正面识别失败";
                 throw new ServiceException(ResultCode.ERROR, "身份证正面识别失败: " + errorMessage + " (Code: " + errorCode + ")");
            }

            return idCardInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (TeaException e) {
             // 处理TeaException，通常包含API返回的错误信息
            log.error("身份证正面识别TeaException", e);
            String errorCode = e.getCode();
            String errorMessage = e.getMessage();
            // TeaException的Data字段可能包含Recommend信息或其他详细错误
            if (e.getData() instanceof Map) {
                Map<String, Object> errorData = (Map<String, Object>) e.getData();
                if (errorData.containsKey("Recommend")) {
                     log.error("TeaException Recommend: {}", errorData.get("Recommend"));
                }
                 if (errorData.containsKey("Message")) {
                      errorMessage = (String) errorData.get("Message");
                 }
            }
            throw new ServiceException(ResultCode.ERROR, "身份证正面识别API错误: " + errorMessage + " (Code: " + errorCode + ")");
        } catch (IOException e) {
            log.error("身份证正面识别IO异常", e);
            throw new ServiceException(ResultCode.ERROR, "身份证图片处理异常");
        } catch (Exception e) {
            log.error("身份证正面识别异常", e);
            throw new ServiceException(ResultCode.ERROR, "身份证正面识别异常: " + e.getMessage());
        }
    }

    /**
     * 识别身份证背面
     *
     * @param file 身份证背面图片
     * @return 身份证信息
     */
    @Override
    public IdCardInfo recognizeIdCardBack(MultipartFile file) {
        try {
            if (this.client == null) {
                throw new ServiceException(ResultCode.ERROR, "OCR服务未正确初始化，请检查配置");
            }
            
            String base64Image = Base64.getEncoder().encodeToString(file.getBytes());

            // 根据SDK概述文档示例，使用RecognizeAllTextRequest并通过Type区分
            RecognizeAllTextRequest recognizeAllTextRequest = new RecognizeAllTextRequest()
                    .setType("IdCard")
                    .setBody(new ByteArrayInputStream(file.getBytes()));

            RuntimeOptions runtime = new RuntimeOptions();

            // 调用SDK进行识别
            RecognizeAllTextResponse response = this.client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);
            // log.info("身份证背面识别结果: {}", JSON.toJSONString(response.getBody()));

            // 解析SDK返回结果 - 根据SDK概述文档示例结构调整
            IdCardInfo idCardInfo = new IdCardInfo();
             if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyData data = response.getBody().getData();
                // 示例结构: Data -> SubImages[] -> KvInfo -> Data
                 if (data.getSubImages() != null && !data.getSubImages().isEmpty()) {
                    RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages subImage = data.getSubImages().get(0);
                    if (subImage.getKvInfo() != null && subImage.getKvInfo().getData() != null) {
                        JSONObject backInfo = JSON.parseObject(JSON.toJSONString(subImage.getKvInfo().getData()));
                         if (backInfo != null) {
                            idCardInfo.setIssueAuthority(backInfo.getString("issueAuthority")); 
                            
                            String validPeriodString = backInfo.getString("validPeriod");
                            if (StrUtil.isNotEmpty(validPeriodString)) {
                                idCardInfo.setValidPeriod(validPeriodString.replace(".", "-")); // Store the period as YYYY-MM-DD-YYYY-MM-DD or YYYY-MM-DD-长期
                                String[] dates = validPeriodString.split("-");
                                if (dates.length > 0 && StrUtil.isNotEmpty(dates[0])) {
                                    idCardInfo.setValidFrom(dates[0].replace(".", "-"));
                                }
                                if (dates.length > 1 && StrUtil.isNotEmpty(dates[1])) {
                                    idCardInfo.setValidTo(dates[1].replace(".", "-"));
                                }
                            }
                            
                            // 判断并标记是否为长期有效 (确保 IdCardInfo.checkAndSetLongTerm() 能正确处理新的 validTo 格式)
                            idCardInfo.checkAndSetLongTerm();
                        }
                    }
                }
            } else {
                 // 处理SDK返回错误
                 String errorCode = response != null && response.getBody() != null && response.getBody().getCode() != null ? response.getBody().getCode() : "UNKNOWN_SDK_ERROR";
                 String errorMessage = response != null && response.getBody() != null && response.getBody().getMessage() != null ? response.getBody().getMessage() : "身份证背面识别失败";
                 throw new ServiceException(ResultCode.ERROR, "身份证背面识别失败: " + errorMessage + " (Code: " + errorCode + ")");
            }

            return idCardInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (TeaException e) {
            // 处理TeaException
            log.error("身份证背面识别TeaException", e);
            String errorCode = e.getCode();
            String errorMessage = e.getMessage();
             if (e.getData() instanceof Map) {
                Map<String, Object> errorData = (Map<String, Object>) e.getData();
                if (errorData.containsKey("Recommend")) {
                     log.error("TeaException Recommend: {}", errorData.get("Recommend"));
                }
                 if (errorData.containsKey("Message")) {
                      errorMessage = (String) errorData.get("Message");
                 }
            }
            throw new ServiceException(ResultCode.ERROR, "身份证背面识: " + errorMessage + " (Code: " + errorCode + ")");
        } catch (IOException e) {
            log.error("身份证背面识别IO异常", e);
            throw new ServiceException(ResultCode.ERROR, "身份证图片处理异常");
        } catch (Exception e) {
            log.error("身份证背面识别异常", e);
            throw new ServiceException(ResultCode.ERROR, "身份证背面识别异常: " + e.getMessage());
        }
    }

    /**
     * 识别营业执照
     *
     * @param file 营业执照图片
     * @return 营业执照信息
     */
    @Override
    public BusinessLicenseInfo recognizeBusinessLicense(MultipartFile file) {
        try {
            if (this.client == null) {
                throw new ServiceException(ResultCode.ERROR, "OCR服务未正确初始化，请检查配置");
            }
            
            // String base64Image = Base64.getEncoder().encodeToString(file.getBytes()); // Base64字符串方式不再推荐用于新SDK

             // 根据SDK概述文档示例，使用RecognizeAllTextRequest并通过Type区分
            RecognizeAllTextRequest recognizeAllTextRequest = new RecognizeAllTextRequest()
                    .setType("BusinessLicense")
                     // 使用InputStream传递图片数据
                    .setBody(new ByteArrayInputStream(file.getBytes()));

            RuntimeOptions runtime = new RuntimeOptions();

            // 调用SDK进行识别
            RecognizeAllTextResponse response = this.client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);
            // log.info("阿里云OCR营业执照识别原始SDK响应: {}", JSON.toJSONString(response.getBody()));

            // 解析SDK返回结果
            BusinessLicenseInfo businessLicenseInfo = new BusinessLicenseInfo();
             if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyData data = response.getBody().getData();
                // 示例结构: Data -> SubImages[] -> KvInfo -> Data
                 if (data.getSubImages() != null && !data.getSubImages().isEmpty()) {
                    RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages subImage = data.getSubImages().get(0);
                    if (subImage.getKvInfo() != null && subImage.getKvInfo().getData() != null) {
                        JSONObject licenseInfo = JSON.parseObject(JSON.toJSONString(subImage.getKvInfo().getData()));
                          if (licenseInfo != null) {
                              // 更新字段名以匹配您日志中SDK的实际输出
                              businessLicenseInfo.setCompanyName(licenseInfo.getString("companyName"));
                              businessLicenseInfo.setLicenseNum(licenseInfo.getString("creditCode")); 
                              businessLicenseInfo.setLegalPerson(licenseInfo.getString("legalPerson")); 
                              businessLicenseInfo.setRegisteredCapital(licenseInfo.getString("registeredCapital"));
                              businessLicenseInfo.setBusinessScope(licenseInfo.getString("businessScope")); 
                              businessLicenseInfo.setAddress(licenseInfo.getString("businessAddress")); 

                              // 保留其他字段的原有SDK名称，如果它们也变了，需要您提供新的名称
                              businessLicenseInfo.setEstablishDate(licenseInfo.getString("EstablishDate")); 
                              businessLicenseInfo.setBusinessTerm(licenseInfo.getString("ValidPeriod")); 
                              businessLicenseInfo.setCompanyType(licenseInfo.getString("Type")); 
                          }
                    }
                }
            } else {
                 // 处理SDK返回错误
                 String errorCode = response != null && response.getBody() != null && response.getBody().getCode() != null ? response.getBody().getCode() : "UNKNOWN_SDK_ERROR";
                 String errorMessage = response != null && response.getBody() != null && response.getBody().getMessage() != null ? response.getBody().getMessage() : "营业执照识别失败";
                 throw new ServiceException(ResultCode.ERROR, "营业执照识别失败: " + errorMessage + " (Code: " + errorCode + ")");
            }
            return businessLicenseInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (TeaException e) {
             // 处理TeaException
            log.error("营业执照识别TeaException", e);
            String errorCode = e.getCode();
            String errorMessage = e.getMessage();
             if (e.getData() instanceof Map) {
                Map<String, Object> errorData = (Map<String, Object>) e.getData();
                if (errorData.containsKey("Recommend")) {
                     log.error("TeaException Recommend: {}", errorData.get("Recommend"));
                }
                 if (errorData.containsKey("Message")) {
                      errorMessage = (String) errorData.get("Message");
                 }
            }
            throw new ServiceException(ResultCode.ERROR, "营业执照识别API错误: " + errorMessage + " (Code: " + errorCode + ")");
        } catch (IOException e) {
            log.error("营业执照识别IO异常", e);
            throw new ServiceException(ResultCode.ERROR, "营业执照图片处理异常");
        } catch (Exception e) {
            log.error("营业执照识别异常", e);
            throw new ServiceException(ResultCode.ERROR, "营业执照识别异常: " + e.getMessage());
        }
    }
} 