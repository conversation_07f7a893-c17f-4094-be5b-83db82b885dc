package cn.lili.modules.ocr.serviceimpl;

import cn.hutool.core.util.StrUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.ocr.entity.dto.BusinessLicenseInfo;
import cn.lili.modules.ocr.entity.dto.IdCardInfo;
import cn.lili.modules.ocr.service.OcrService;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.OcrSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.service.SettingService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.io.ByteArrayInputStream;

/**
 * OCR service implementation
 *
 * <AUTHOR>
 * @since 2021/2/20 10:10
 */
@Slf4j
@Service
public class OcrServiceImpl implements OcrService {

    @Autowired
    private SettingService settingService;

    private Client client;

    @PostConstruct
    public void init() throws Exception {
        try {
            // Get OCR configuration from database
            Setting setting = settingService.get(SettingEnum.OCR_SETTING.name());
            if (setting == null || StrUtil.isEmpty(setting.getSettingValue())) {
                log.warn("OCR configuration not found, please configure OCR settings in admin panel");
                return;
            }

            // Parse OCR configuration
            Gson gson = new Gson();
            OcrSetting ocrSetting = gson.fromJson(setting.getSettingValue(), OcrSetting.class);

            if (ocrSetting == null || !ocrSetting.getEnabled()) {
                log.warn("OCR service is not enabled");
                return;
            }

            if (StrUtil.isEmpty(ocrSetting.getAccessKeyId()) || StrUtil.isEmpty(ocrSetting.getAccessKeySecret())) {
                log.error("OCR authentication info not configured! Please configure accessKeyId and accessKeySecret in admin panel");
                return;
            }

            log.info("Initializing OCR client, accessKeyId empty: {}, accessKeySecret empty: {}",
                     StrUtil.isEmpty(ocrSetting.getAccessKeyId()), StrUtil.isEmpty(ocrSetting.getAccessKeySecret()));

            Config config = new Config()
                    .setAccessKeyId(ocrSetting.getAccessKeyId())
                    .setAccessKeySecret(ocrSetting.getAccessKeySecret());

            // Set region endpoint
            String regionId = StrUtil.isNotEmpty(ocrSetting.getRegionId()) ? ocrSetting.getRegionId() : "cn-hangzhou";
            config.endpoint = "ocr-api." + regionId + ".aliyuncs.com";

            this.client = new Client(config);
            log.info("OCR client initialized successfully, region: {}", regionId);

        } catch (Exception e) {
            log.error("OCR client initialization failed", e);
        }
    }

    /**
     * Recognize ID card front side
     *
     * @param file ID card front side image
     * @return ID card information
     */
    @Override
    public IdCardInfo recognizeIdCardFront(MultipartFile file) {
        try {
            if (this.client == null) {
                throw new ServiceException(ResultCode.ERROR, "OCR service not initialized properly, please check configuration");
            }

            String base64Image = Base64.getEncoder().encodeToString(file.getBytes());

            // Use RecognizeAllTextRequest with Type parameter according to SDK documentation
            RecognizeAllTextRequest recognizeAllTextRequest = new RecognizeAllTextRequest()
                    .setType("IdCard")
                    .setBody(new ByteArrayInputStream(file.getBytes()));

            RuntimeOptions runtime = new RuntimeOptions();

            // Call SDK for recognition
            RecognizeAllTextResponse response = this.client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);

            // Parse SDK response according to documentation structure
            IdCardInfo idCardInfo = new IdCardInfo();
            if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyData data = response.getBody().getData();
                // Structure: Data -> SubImages[] -> KvInfo -> Data
                 if (data.getSubImages() != null && !data.getSubImages().isEmpty()) {
                    RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages subImage = data.getSubImages().get(0);
                    if (subImage.getKvInfo() != null && subImage.getKvInfo().getData() != null) {
                        JSONObject faceInfo = JSON.parseObject(JSON.toJSONString(subImage.getKvInfo().getData()));
                         if (faceInfo != null) {
                            idCardInfo.setName(faceInfo.getString("name"));
                            idCardInfo.setGender(faceInfo.getString("gender"));
                            idCardInfo.setEthnicity(faceInfo.getString("nationality"));
                            idCardInfo.setBirthDate(faceInfo.getString("birthDate"));
                            idCardInfo.setAddress(faceInfo.getString("address"));
                            idCardInfo.setIdNumber(faceInfo.getString("idNumber"));
                        }
                    }
                }
            } else {
                 // Handle SDK error response
                 String errorCode = response != null && response.getBody() != null && response.getBody().getCode() != null ? response.getBody().getCode() : "UNKNOWN_SDK_ERROR";
                 String errorMessage = response != null && response.getBody() != null && response.getBody().getMessage() != null ? response.getBody().getMessage() : "ID card front recognition failed";
                 throw new ServiceException(ResultCode.ERROR, "ID card front recognition failed: " + errorMessage + " (Code: " + errorCode + ")");
            }

            return idCardInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (TeaException e) {
             // Handle TeaException which usually contains API error information
            log.error("ID card front recognition TeaException", e);
            String errorCode = e.getCode();
            String errorMessage = e.getMessage();
            // TeaException Data field may contain Recommend info or other detailed errors
            if (e.getData() instanceof Map) {
                Map<String, Object> errorData = (Map<String, Object>) e.getData();
                if (errorData.containsKey("Recommend")) {
                     log.error("TeaException Recommend: {}", errorData.get("Recommend"));
                }
                 if (errorData.containsKey("Message")) {
                      errorMessage = (String) errorData.get("Message");
                 }
            }
            throw new ServiceException(ResultCode.ERROR, "ID card front recognition API error: " + errorMessage + " (Code: " + errorCode + ")");
        } catch (IOException e) {
            log.error("ID card front recognition IO exception", e);
            throw new ServiceException(ResultCode.ERROR, "ID card image processing exception");
        } catch (Exception e) {
            log.error("ID card front recognition exception", e);
            throw new ServiceException(ResultCode.ERROR, "ID card front recognition exception: " + e.getMessage());
        }
    }

    /**
     * Recognize ID card back side
     *
     * @param file ID card back side image
     * @return ID card information
     */
    @Override
    public IdCardInfo recognizeIdCardBack(MultipartFile file) {
        try {
            if (this.client == null) {
                throw new ServiceException(ResultCode.ERROR, "OCR service not initialized properly, please check configuration");
            }

            String base64Image = Base64.getEncoder().encodeToString(file.getBytes());

            // Use RecognizeAllTextRequest with Type parameter according to SDK documentation
            RecognizeAllTextRequest recognizeAllTextRequest = new RecognizeAllTextRequest()
                    .setType("IdCard")
                    .setBody(new ByteArrayInputStream(file.getBytes()));

            RuntimeOptions runtime = new RuntimeOptions();

            // Call SDK for recognition
            RecognizeAllTextResponse response = this.client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);

            // Parse SDK response according to documentation structure
            IdCardInfo idCardInfo = new IdCardInfo();
             if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyData data = response.getBody().getData();
                // Structure: Data -> SubImages[] -> KvInfo -> Data
                 if (data.getSubImages() != null && !data.getSubImages().isEmpty()) {
                    RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages subImage = data.getSubImages().get(0);
                    if (subImage.getKvInfo() != null && subImage.getKvInfo().getData() != null) {
                        JSONObject backInfo = JSON.parseObject(JSON.toJSONString(subImage.getKvInfo().getData()));
                         if (backInfo != null) {
                            idCardInfo.setIssueAuthority(backInfo.getString("issueAuthority"));

                            String validPeriodString = backInfo.getString("validPeriod");
                            if (StrUtil.isNotEmpty(validPeriodString)) {
                                idCardInfo.setValidPeriod(validPeriodString.replace(".", "-"));
                                String[] dates = validPeriodString.split("-");
                                if (dates.length > 0 && StrUtil.isNotEmpty(dates[0])) {
                                    idCardInfo.setValidFrom(dates[0].replace(".", "-"));
                                }
                                if (dates.length > 1 && StrUtil.isNotEmpty(dates[1])) {
                                    idCardInfo.setValidTo(dates[1].replace(".", "-"));
                                }
                            }

                            // Check and set long-term validity
                            idCardInfo.checkAndSetLongTerm();
                        }
                    }
                }
            } else {
                 // Handle SDK error response
                 String errorCode = response != null && response.getBody() != null && response.getBody().getCode() != null ? response.getBody().getCode() : "UNKNOWN_SDK_ERROR";
                 String errorMessage = response != null && response.getBody() != null && response.getBody().getMessage() != null ? response.getBody().getMessage() : "ID card back recognition failed";
                 throw new ServiceException(ResultCode.ERROR, "ID card back recognition failed: " + errorMessage + " (Code: " + errorCode + ")");
            }

            return idCardInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (TeaException e) {
            // Handle TeaException
            log.error("ID card back recognition TeaException", e);
            String errorCode = e.getCode();
            String errorMessage = e.getMessage();
             if (e.getData() instanceof Map) {
                Map<String, Object> errorData = (Map<String, Object>) e.getData();
                if (errorData.containsKey("Recommend")) {
                     log.error("TeaException Recommend: {}", errorData.get("Recommend"));
                }
                 if (errorData.containsKey("Message")) {
                      errorMessage = (String) errorData.get("Message");
                 }
            }
            throw new ServiceException(ResultCode.ERROR, "ID card back recognition API error: " + errorMessage + " (Code: " + errorCode + ")");
        } catch (IOException e) {
            log.error("ID card back recognition IO exception", e);
            throw new ServiceException(ResultCode.ERROR, "ID card image processing exception");
        } catch (Exception e) {
            log.error("ID card back recognition exception", e);
            throw new ServiceException(ResultCode.ERROR, "ID card back recognition exception: " + e.getMessage());
        }
    }

    /**
     * Recognize business license
     *
     * @param file business license image
     * @return business license information
     */
    @Override
    public BusinessLicenseInfo recognizeBusinessLicense(MultipartFile file) {
        try {
            if (this.client == null) {
                throw new ServiceException(ResultCode.ERROR, "OCR service not initialized properly, please check configuration");
            }

            // Use RecognizeAllTextRequest with Type parameter according to SDK documentation
            RecognizeAllTextRequest recognizeAllTextRequest = new RecognizeAllTextRequest()
                    .setType("BusinessLicense")
                    // Use InputStream to pass image data
                    .setBody(new ByteArrayInputStream(file.getBytes()));

            RuntimeOptions runtime = new RuntimeOptions();

            // Call SDK for recognition
            RecognizeAllTextResponse response = this.client.recognizeAllTextWithOptions(recognizeAllTextRequest, runtime);

            // Parse SDK response
            BusinessLicenseInfo businessLicenseInfo = new BusinessLicenseInfo();
             if (response != null && response.getBody() != null && response.getBody().getData() != null) {
                RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyData data = response.getBody().getData();
                // Structure: Data -> SubImages[] -> KvInfo -> Data
                 if (data.getSubImages() != null && !data.getSubImages().isEmpty()) {
                    RecognizeAllTextResponseBody.RecognizeAllTextResponseBodyDataSubImages subImage = data.getSubImages().get(0);
                    if (subImage.getKvInfo() != null && subImage.getKvInfo().getData() != null) {
                        JSONObject licenseInfo = JSON.parseObject(JSON.toJSONString(subImage.getKvInfo().getData()));
                          if (licenseInfo != null) {
                              // Map field names according to SDK output
                              businessLicenseInfo.setCompanyName(licenseInfo.getString("companyName"));
                              businessLicenseInfo.setLicenseNum(licenseInfo.getString("creditCode"));
                              businessLicenseInfo.setLegalPerson(licenseInfo.getString("legalPerson"));
                              businessLicenseInfo.setRegisteredCapital(licenseInfo.getString("registeredCapital"));
                              businessLicenseInfo.setBusinessScope(licenseInfo.getString("businessScope"));
                              businessLicenseInfo.setAddress(licenseInfo.getString("businessAddress"));

                              // Keep other fields with original SDK names
                              businessLicenseInfo.setEstablishDate(licenseInfo.getString("EstablishDate"));
                              businessLicenseInfo.setBusinessTerm(licenseInfo.getString("ValidPeriod"));
                              businessLicenseInfo.setCompanyType(licenseInfo.getString("Type"));
                          }
                    }
                }
            } else {
                 // Handle SDK error response
                 String errorCode = response != null && response.getBody() != null && response.getBody().getCode() != null ? response.getBody().getCode() : "UNKNOWN_SDK_ERROR";
                 String errorMessage = response != null && response.getBody() != null && response.getBody().getMessage() != null ? response.getBody().getMessage() : "Business license recognition failed";
                 throw new ServiceException(ResultCode.ERROR, "Business license recognition failed: " + errorMessage + " (Code: " + errorCode + ")");
            }
            return businessLicenseInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (TeaException e) {
             // Handle TeaException
            log.error("Business license recognition TeaException", e);
            String errorCode = e.getCode();
            String errorMessage = e.getMessage();
             if (e.getData() instanceof Map) {
                Map<String, Object> errorData = (Map<String, Object>) e.getData();
                if (errorData.containsKey("Recommend")) {
                     log.error("TeaException Recommend: {}", errorData.get("Recommend"));
                }
                 if (errorData.containsKey("Message")) {
                      errorMessage = (String) errorData.get("Message");
                 }
            }
            throw new ServiceException(ResultCode.ERROR, "Business license recognition API error: " + errorMessage + " (Code: " + errorCode + ")");
        } catch (IOException e) {
            log.error("Business license recognition IO exception", e);
            throw new ServiceException(ResultCode.ERROR, "Business license image processing exception");
        } catch (Exception e) {
            log.error("Business license recognition exception", e);
            throw new ServiceException(ResultCode.ERROR, "Business license recognition exception: " + e.getMessage());
        }
    }
}