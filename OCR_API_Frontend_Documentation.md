# OCR 服务 API 文档

本文档为前端开发者提供了关于如何集成和使用后端OCR（光学字符识别）服务的指南。
这些服务允许用户上传身份证图片和营业执照图片，并自动提取其中的文本信息。

## 通用说明

- 所有API的请求内容类型 (Content-Type) 均为 `multipart/form-data`。
- 图片文件应通过名为 `file` 的参数进行上传。
- 成功的响应将包含一个标准结构体，其中 `data` 字段包含识别结果。
  ```json
  {
    "success": true,
    "message": "操作成功",
    "code": 200,
    "data": {
      // OCR识别的具体数据
    },
    "timestamp": 1678886400000
  }
  ```
- 失败的响应也会遵循类似的结构，但 `success` 为 `false`，`code` 和 `message` 会指示错误原因。

## API 端点

### 1. 识别身份证正面

此端点用于识别身份证正面信息。

- **URL**: `/common/common/ocr/idcard/front`
- **方法**: `POST`
- **请求参数**:
    - `file`: (`MultipartFile`) 身份证正面图片文件。
- **成功响应 (`data` 字段内容)**:
  ```json
  {
    "name": "张三",
    "sex": "男",
    "ethnicity": "汉", // 对应原字段 idCardOcrDTO.getNationality()，表示民族
    "birthDate": "1990-01-01",
    "address": "浙江省杭州市余杭区文一西路969号",
    "idNumber": "33010019900101001X"
  }
  ```
- **`curl` 示例**:
  ```bash
  curl -X POST \
    /common/common/ocr/idcard/front \
    -H 'Content-Type: multipart/form-data' \
    -F 'file=@/path/to/idcard_front_image.jpg'
  ```

### 2. 识别身份证反面

此端点用于识别身份证反面信息（国徽面）。

- **URL**: `/common/common/ocr/idcard/back`
- **方法**: `POST`
- **请求参数**:
    - `file`: (`MultipartFile`) 身份证反面图片文件。
- **成功响应 (`data` 字段内容)**:
  ```json
  {
    "issueAuthority": "杭州市公安局余杭分局", // 签发机关
    "startDate": "2020-01-01", // 有效期起始日期
    "endDate": "2030-01-01" // 有效期结束日期
  }
  ```
- **`curl` 示例**:
  ```bash
  curl -X POST \
    /common/common/ocr/idcard/back \
    -H 'Content-Type: multipart/form-data' \
    -F 'file=@/path/to/idcard_back_image.jpg'
  ```

### 3. 识别营业执照

此端点用于识别营业执照信息。

- **URL**: `/common/common/ocr/business-license`
- **方法**: `POST`
- **请求参数**:
    - `file`: (`MultipartFile`) 营业执照图片文件。
- **成功响应 (`data` 字段内容)**:
  ```json
  {
    "registrationNumber": "91330100MA2B1B1B1B", // 统一社会信用代码
    "companyName": "示例有限公司",
    "legalRepresentative": "李四",
    "address": "浙江省杭州市余杭区仓前街道某某园区",
    "businessScope": "技术开发、技术服务...", // 经营范围
    "registeredCapital": "100万元人民币",
    "establishmentDate": "2023-01-01", // 成立日期
    "validityPeriod": "2023-01-01至长期", // 营业期限
    "companyType": "有限责任公司" // 公司类型
  }
  ```
- **`curl` 示例**:
  ```bash
  curl -X POST \
    /common/common/ocr/business-license \
    -H 'Content-Type: multipart/form-data' \
    -F 'file=@/path/to/business_license_image.jpg'
  ```

## 错误处理

如果识别失败或请求不合法，API将返回一个包含错误信息的JSON响应。例如：

- **文件过大 / 不支持的文件类型**: 后端可能会有文件大小和类型的限制。
- **OCR识别失败**: 如果图片质量过低或无法识别，将返回特定错误。
- **参数错误**: 如未上传`file`参数。

示例错误响应:
```json
{
  "success": false,
  "message": "OCR识别失败，请确保图片清晰且完整",
  "code": 500, // 具体错误码可能不同
  "data": null,
  "timestamp": 1678886400000
}
```

前端应根据 `success` 字段和 `message` / `code` 字段来处理错误情况，并向用户显示适当的提示。 